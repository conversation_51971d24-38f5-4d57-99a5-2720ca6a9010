<?php
/**
 * Demo Course Button
 *
 * This file demonstrates the conditional course button functionality
 *
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Mock WordPress environment for testing
if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        return $default;
    }
}

if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('esc_html')) {
    function esc_html($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('esc_url')) {
    function esc_url($url) {
        return htmlspecialchars($url, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $key = '', $single = false) {
        // Mock some course-related meta for testing
        $mock_meta = array(
            1 => array(
                '_tutor_course_product_id' => array('1'),
                '_course_id' => array('101'),
            ),
            2 => array(
                // No course meta - regular product
            ),
            3 => array(
                '_tutor_course_product_id' => array('3'),
                '_course_id' => array('103'),
            )
        );
        
        if (isset($mock_meta[$post_id][$key])) {
            return $single ? $mock_meta[$post_id][$key][0] : $mock_meta[$post_id][$key];
        }
        
        return $single ? '' : array();
    }
}

if (!function_exists('get_permalink')) {
    function get_permalink($post_id) {
        return '/course/' . $post_id . '/';
    }
}

if (!function_exists('is_user_logged_in')) {
    function is_user_logged_in() {
        return isset($_GET['logged_in']) && $_GET['logged_in'] === '1';
    }
}

if (!function_exists('get_current_user_id')) {
    function get_current_user_id() {
        return is_user_logged_in() ? 123 : 0;
    }
}

// Mock WooCommerce functions
if (!function_exists('wc_get_orders')) {
    function wc_get_orders($args) {
        // Mock orders for testing
        $user_id = get_current_user_id();
        $purchased = isset($_GET['purchased']) && $_GET['purchased'] === '1';

        if (!$user_id || !$purchased) {
            return array();
        }

        // Create mock order items
        $item1 = new MockOrderItem(1); // Course product 1 is purchased
        $item3 = new MockOrderItem(3); // Course product 3 is purchased

        // Create mock order
        $mock_order = new MockOrder(array($item1, $item3));

        return array($mock_order);
    }
}

// Include the course button class
require_once 'includes/class-woo-custom-course-button.php';

// Mock product class
class MockProduct {
    private $id;
    private $name;
    
    public function __construct($id, $name) {
        $this->id = $id;
        $this->name = $name;
    }
    
    public function get_id() {
        return $this->id;
    }
    
    public function get_name() {
        return $this->name;
    }
}

// Mock order class
class MockOrder {
    private $items;
    
    public function __construct($items) {
        $this->items = $items;
    }
    
    public function get_items() {
        return $this->items;
    }
}

// Mock order item class
class MockOrderItem {
    private $product_id;
    
    public function __construct($product_id) {
        $this->product_id = $product_id;
    }
    
    public function get_product_id() {
        return $this->product_id;
    }
}

// Test products
$test_products = array(
    new MockProduct(1, 'PHP Kursu - Başlangıçtan İleri Seviyeye'),
    new MockProduct(2, 'Normal Ürün - T-Shirt'),
    new MockProduct(3, 'JavaScript Kursu - Modern Web Geliştirme'),
    new MockProduct(4, 'Başka Normal Ürün - Kahve Fincanı')
);

// Test scenarios
$scenarios = array(
    array('name' => 'Giriş yapmamış kullanıcı', 'logged_in' => false, 'purchased' => false),
    array('name' => 'Giriş yapmış, satın almamış', 'logged_in' => true, 'purchased' => false),
    array('name' => 'Giriş yapmış, satın almış', 'logged_in' => true, 'purchased' => true),
);

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Woo Custom - Course Button Demo</title>
    <link rel="stylesheet" href="assets/css/woo-custom.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .scenario-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .product-demo {
            border: 1px solid #ddd;
            margin: 15px 0;
            padding: 20px;
            border-radius: 5px;
            background: white;
        }
        .button-demo {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .normal-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: #0073aa;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 600;
            font-size: 16px;
            border: none;
            cursor: pointer;
        }
        .normal-button:hover {
            background: #005a87;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: 500;
            margin: 5px 0;
        }
        .status.course { background: #e8f5e8; color: #2e7d32; }
        .status.normal { background: #fff3e0; color: #f57c00; }
        .status.purchased { background: #e3f2fd; color: #1976d2; }
        .status.not-purchased { background: #fce4ec; color: #c2185b; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Woo Custom - Course Button Demo</h1>
        <p>Bu sayfa, kurs ürünleri için koşullu buton görünümünü test eder.</p>
        
        <?php foreach ($scenarios as $scenario): ?>
            <div class="scenario-section">
                <h2><?php echo esc_html($scenario['name']); ?></h2>
                <p>
                    <strong>Durum:</strong> 
                    <?php if ($scenario['logged_in']): ?>
                        <span class="status purchased">Giriş Yapmış</span>
                    <?php else: ?>
                        <span class="status not-purchased">Giriş Yapmamış</span>
                    <?php endif; ?>
                    
                    <?php if ($scenario['purchased']): ?>
                        <span class="status purchased">Kursları Satın Almış</span>
                    <?php else: ?>
                        <span class="status not-purchased">Kursları Satın Almamış</span>
                    <?php endif; ?>
                </p>
                
                <?php
                // Set mock environment for this scenario
                $_GET['logged_in'] = $scenario['logged_in'] ? '1' : '0';
                $_GET['purchased'] = $scenario['purchased'] ? '1' : '0';
                
                $course_button = WooCustom_Course_Button::instance();
                
                foreach ($test_products as $product):
                    global $product as $original_product;
                    $product = $product; // Set global product for testing
                    
                    // Test if course ID exists
                    $reflection = new ReflectionClass($course_button);
                    $method = $reflection->getMethod('get_course_id_by_product');
                    $method->setAccessible(true);
                    $course_id = $method->invoke($course_button, $product->get_id());
                    
                    // Test if user has purchased
                    $method2 = $reflection->getMethod('has_user_purchased_course');
                    $method2->setAccessible(true);
                    $has_purchased = $method2->invoke($course_button, $product->get_id());
                ?>
                    <div class="product-demo">
                        <h3><?php echo esc_html($product->get_name()); ?></h3>
                        <p><strong>Ürün ID:</strong> <?php echo $product->get_id(); ?></p>
                        
                        <?php if ($course_id): ?>
                            <div class="status course">✓ Kursa Bağlı Ürün (Kurs ID: <?php echo $course_id; ?>)</div>
                        <?php else: ?>
                            <div class="status normal">○ Normal Ürün</div>
                        <?php endif; ?>
                        
                        <?php if ($course_id): ?>
                            <?php if ($has_purchased): ?>
                                <div class="status purchased">✓ Kullanıcı Bu Kursu Satın Almış</div>
                            <?php else: ?>
                                <div class="status not-purchased">○ Kullanıcı Bu Kursu Satın Almamış</div>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <div class="button-demo">
                            <strong>Gösterilecek Buton:</strong><br><br>
                            <?php if ($course_id && $has_purchased): ?>
                                <!-- Continue Course Button -->
                                <a href="<?php echo esc_url(get_permalink($course_id)); ?>" class="single_add_to_cart_button button alt woo-custom-continue-course">
                                    <span class="course-button-icon">📚</span>
                                    <span class="course-button-text">Kursa Devam Et</span>
                                </a>
                            <?php else: ?>
                                <!-- Normal Add to Cart Button -->
                                <button class="normal-button">
                                    <span>🛒</span>
                                    <span>Sepete Ekle</span>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php
                    $product = $original_product; // Restore original product
                endforeach;
                ?>
            </div>
        <?php endforeach; ?>
        
        <div class="scenario-section">
            <h2>Test Linkleri</h2>
            <p>Farklı senaryoları test etmek için aşağıdaki linkleri kullanın:</p>
            <ul>
                <li><a href="?logged_in=0&purchased=0">Giriş yapmamış kullanıcı</a></li>
                <li><a href="?logged_in=1&purchased=0">Giriş yapmış, satın almamış</a></li>
                <li><a href="?logged_in=1&purchased=1">Giriş yapmış, satın almış</a></li>
            </ul>
        </div>
        
        <div class="scenario-section">
            <h2>Beklenen Davranış</h2>
            <ul>
                <li><strong>Normal ürünler:</strong> Her zaman "Sepete Ekle" butonu gösterilir</li>
                <li><strong>Kurs ürünleri (satın alınmamış):</strong> "Sepete Ekle" butonu gösterilir</li>
                <li><strong>Kurs ürünleri (satın alınmış):</strong> "Kursa Devam Et" butonu gösterilir</li>
                <li><strong>Giriş yapmamış kullanıcılar:</strong> Hiçbir ürün satın alınmış olarak görünmez</li>
            </ul>
        </div>
    </div>
</body>
</html>
