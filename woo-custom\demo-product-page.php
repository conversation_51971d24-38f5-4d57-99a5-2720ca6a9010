<?php
/**
 * Demo Product Page
 *
 * This file creates a demo product page to test the course info tab
 *
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Mock WordPress environment for testing
if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        return $default;
    }
}

if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('esc_html')) {
    function esc_html($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $key = '', $single = false) {
        // Mock some course-related meta for testing
        $mock_meta = array(
            1 => array(
                '_tutor_course_product_id' => array('1'),
                '_course_id' => array('101'),
            ),
            2 => array(
                // No course meta - regular product
            ),
            3 => array(
                '_tutor_course_product_id' => array('3'),
                '_course_id' => array('103'),
            )
        );
        
        if (isset($mock_meta[$post_id][$key])) {
            return $single ? $mock_meta[$post_id][$key][0] : $mock_meta[$post_id][$key];
        }
        
        return $single ? '' : array();
    }
}

// Include the course info class
require_once 'includes/class-woo-custom-course-info.php';

// Mock product class
class MockProduct {
    private $id;
    private $name;
    
    public function __construct($id, $name) {
        $this->id = $id;
        $this->name = $name;
    }
    
    public function get_id() {
        return $this->id;
    }
    
    public function get_name() {
        return $this->name;
    }
}

// Test products
$test_products = array(
    new MockProduct(1, 'PHP Kursu - Başlangıçtan İleri Seviyeye'),
    new MockProduct(2, 'Normal Ürün - T-Shirt'),
    new MockProduct(3, 'JavaScript Kursu - Modern Web Geliştirme'),
    new MockProduct(4, 'Başka Normal Ürün - Kahve Fincanı')
);

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Woo Custom - Demo Ürün Sayfası</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-card {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 20px;
            border-radius: 5px;
            background: #fafafa;
        }
        .product-tabs {
            margin-top: 20px;
        }
        .tab-list {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            cursor: pointer;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background: white;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }
        .tab.course-info {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .tab-content {
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 5px;
            background: white;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Woo Custom - Demo Ürün Sayfası</h1>
        <p>Bu sayfa, kurs bilgileri sekmesinin sadece kursa bağlı ürünlerde görünüp görünmediğini test eder.</p>
        
        <?php
        $course_info = WooCustom_Course_Info::instance();
        
        foreach ($test_products as $product) {
            global $product as $original_product;
            $product = $product; // Set global product for testing
            
            echo '<div class="product-card">';
            echo '<h2>' . esc_html($product->get_name()) . '</h2>';
            echo '<p><strong>Ürün ID:</strong> ' . $product->get_id() . '</p>';
            
            // Test if course ID exists
            $reflection = new ReflectionClass($course_info);
            $method = $reflection->getMethod('get_course_id_by_product');
            $method->setAccessible(true);
            $course_id = $method->invoke($course_info, $product->get_id());
            
            if ($course_id) {
                echo '<div class="result success">';
                echo '<strong>✓ Kursa Bağlı Ürün</strong><br>';
                echo 'Kurs ID: ' . $course_id;
                echo '</div>';
            } else {
                echo '<div class="result warning">';
                echo '<strong>○ Normal Ürün</strong><br>';
                echo 'Bu ürün herhangi bir kursa bağlı değil.';
                echo '</div>';
            }
            
            // Test tab visibility
            $tabs = array(
                'description' => array('title' => 'Açıklama', 'priority' => 10),
                'reviews' => array('title' => 'Değerlendirmeler', 'priority' => 30)
            );
            
            $tabs_with_course_info = $course_info->add_course_info_tab($tabs);
            $tab_visible = isset($tabs_with_course_info['course_info']);
            
            echo '<div class="product-tabs">';
            echo '<div class="tab-list">';
            
            foreach ($tabs_with_course_info as $key => $tab) {
                $class = 'tab';
                if ($key === 'course_info') {
                    $class .= ' course-info';
                }
                echo '<div class="' . $class . '">' . $tab['title'] . '</div>';
            }
            
            echo '</div>';
            
            if ($tab_visible) {
                echo '<div class="result success">';
                echo '<strong>✓ Kurs Bilgileri Sekmesi Görünüyor</strong><br>';
                echo 'Bu ürün için "Kurs Bilgileri" sekmesi eklendi.';
                echo '</div>';
            } else {
                echo '<div class="result info">';
                echo '<strong>○ Kurs Bilgileri Sekmesi Görünmüyor</strong><br>';
                echo 'Bu ürün kursa bağlı olmadığı için "Kurs Bilgileri" sekmesi eklenmedi.';
                echo '</div>';
            }
            
            echo '</div>'; // product-tabs
            echo '</div>'; // product-card
            
            $product = $original_product; // Restore original product
        }
        ?>
        
        <div class="result info">
            <h3>Test Sonucu</h3>
            <p><strong>Beklenen Davranış:</strong> "Kurs Bilgileri" sekmesi sadece bir kursa bağlı olan ürünlerde görünmelidir.</p>
            <p><strong>Gerçek Davranış:</strong> Yukarıdaki test sonuçlarına bakın.</p>
            <p><strong>Başarı Kriteri:</strong> Kurs ID'si olan ürünlerde sekme görünüyor, olmayanlarda görünmüyor.</p>
        </div>
    </div>
</body>
</html>
