<?php
/**
 * Test Tab Visibility
 *
 * This file tests whether the course info tab appears only for course-related products
 *
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Tab Visibility
 */
class WooCustom_Tab_Visibility_Test {
    
    public function __construct() {
        add_action('init', array($this, 'run_tests'));
    }
    
    /**
     * Run all tests
     */
    public function run_tests() {
        // Only run tests if user is admin and test parameter is set
        if (!current_user_can('manage_options') || !isset($_GET['test_tab_visibility'])) {
            return;
        }
        
        echo '<div style="background: white; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h2>Woo Custom - Tab Visibility Test</h2>';
        
        $this->test_tab_logic();
        
        echo '</div>';
        exit;
    }
    
    /**
     * Test tab visibility logic
     */
    private function test_tab_logic() {
        echo '<h3><PERSON>rs Bilgileri Sekmesi Görünürlük Testi</h3>';
        
        if (!class_exists('WooCustom_Course_Info')) {
            echo '<p style="color: red;">✗ WooCustom_Course_Info sınıfı bulunamadı</p>';
            return;
        }
        
        $course_info = WooCustom_Course_Info::instance();
        
        // Get all products
        $products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => 20
        ));
        
        if (empty($products)) {
            echo '<p style="color: orange;">⚠ Test için ürün bulunamadı</p>';
            return;
        }
        
        echo '<p>Toplam ' . count($products) . ' ürün test ediliyor...</p>';
        echo '<table style="border-collapse: collapse; width: 100%; margin-top: 10px;">';
        echo '<tr style="background: #f0f0f0;">';
        echo '<th style="border: 1px solid #ccc; padding: 8px;">Ürün Adı</th>';
        echo '<th style="border: 1px solid #ccc; padding: 8px;">Ürün ID</th>';
        echo '<th style="border: 1px solid #ccc; padding: 8px;">Kurs ID</th>';
        echo '<th style="border: 1px solid #ccc; padding: 8px;">Sekme Görünür mü?</th>';
        echo '</tr>';
        
        $course_products = 0;
        $non_course_products = 0;
        
        foreach ($products as $product) {
            // Use reflection to access private method
            $reflection = new ReflectionClass($course_info);
            $method = $reflection->getMethod('get_course_id_by_product');
            $method->setAccessible(true);
            
            $course_id = $method->invoke($course_info, $product->ID);
            
            // Simulate the tab logic
            $tabs = array('description' => array(), 'reviews' => array());
            $fake_product = (object) array('ID' => $product->ID);
            
            // Mock global $product
            global $product as $original_product;
            $product = $fake_product;
            
            $tabs_with_course_info = $course_info->add_course_info_tab($tabs);
            $tab_visible = isset($tabs_with_course_info['course_info']);
            
            // Restore original product
            $product = $original_product;
            
            $row_color = $tab_visible ? '#e8f5e8' : '#f8f8f8';
            
            echo '<tr style="background: ' . $row_color . ';">';
            echo '<td style="border: 1px solid #ccc; padding: 8px;">' . esc_html($product->post_title) . '</td>';
            echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $product->ID . '</td>';
            echo '<td style="border: 1px solid #ccc; padding: 8px;">' . ($course_id ? $course_id : '-') . '</td>';
            echo '<td style="border: 1px solid #ccc; padding: 8px;">';
            
            if ($tab_visible) {
                echo '<span style="color: green;">✓ Evet</span>';
                $course_products++;
            } else {
                echo '<span style="color: gray;">○ Hayır</span>';
                $non_course_products++;
            }
            
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
        
        echo '<div style="margin-top: 20px; padding: 15px; background: #f0f8ff; border: 1px solid #0073aa;">';
        echo '<h4>Test Sonuçları:</h4>';
        echo '<p><strong>Kurs bilgileri sekmesi görünen ürünler:</strong> ' . $course_products . '</p>';
        echo '<p><strong>Kurs bilgileri sekmesi görünmeyen ürünler:</strong> ' . $non_course_products . '</p>';
        echo '<p><strong>Beklenen davranış:</strong> Sekme sadece bir kursa bağlı olan ürünlerde görünmelidir.</p>';
        
        if ($course_products > 0) {
            echo '<p style="color: green;">✓ Kurs bilgileri sekmesi kursa bağlı ürünlerde görünüyor.</p>';
        } else {
            echo '<p style="color: orange;">⚠ Hiçbir üründe kurs bilgileri sekmesi görünmüyor. Bu normal olabilir eğer hiçbir ürün kursa bağlı değilse.</p>';
        }
        
        if ($non_course_products > 0) {
            echo '<p style="color: green;">✓ Kursa bağlı olmayan ürünlerde sekme görünmüyor.</p>';
        }
        
        echo '</div>';
    }
}

// Initialize test if needed
if (isset($_GET['test_tab_visibility'])) {
    new WooCustom_Tab_Visibility_Test();
}

// Add admin notice for easy testing
add_action('admin_notices', function() {
    if (current_user_can('manage_options')) {
        $test_url = add_query_arg('test_tab_visibility', '1', home_url());
        echo '<div class="notice notice-info">';
        echo '<p><strong>Woo Custom Tab Visibility Test:</strong> ';
        echo '<a href="' . esc_url($test_url) . '" target="_blank">Sekme görünürlüğünü test et</a>';
        echo '</p>';
        echo '</div>';
    }
});
?>
