<?php
/**
 * WooCustom Course Button Class
 *
 * Handles conditional button display for course-related WooCommerce products
 *
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WooCustom Course Button Class
 */
class WooCustom_Course_Button {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Check and replace add to cart button for course products
        add_action('woocommerce_single_product_summary', array($this, 'maybe_replace_add_to_cart_button'), 25);

        // Remove default add to cart button for purchased courses
        add_filter('woocommerce_is_purchasable', array($this, 'maybe_disable_purchase'), 10, 2);

        // Alternative hook for themes that don't use standard hooks
        add_action('woocommerce_before_add_to_cart_form', array($this, 'check_and_hide_form'), 5);
    }
    
    /**
     * Maybe replace add to cart button with continue course button
     */
    public function maybe_replace_add_to_cart_button() {
        global $product;

        if (!$product) {
            return;
        }

        // Check if this is a course product
        $course_id = $this->get_course_id_by_product($product->get_id());
        if (!$course_id) {
            return;
        }

        // Check if user has purchased this course
        if ($this->has_user_purchased_course($product->get_id())) {
            // Remove default add to cart button
            remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);

            // Add continue course button
            add_action('woocommerce_single_product_summary', array($this, 'display_continue_course_button'), 30);
        }
    }

    /**
     * Check and hide add to cart form for purchased courses
     */
    public function check_and_hide_form() {
        global $product;

        if (!$product) {
            return;
        }

        // Check if this is a course product
        $course_id = $this->get_course_id_by_product($product->get_id());
        if (!$course_id) {
            return;
        }

        // Check if user has purchased this course
        if ($this->has_user_purchased_course($product->get_id())) {
            // Hide the entire add to cart form
            add_action('woocommerce_before_add_to_cart_button', array($this, 'start_form_buffer'), 1);
            add_action('woocommerce_after_add_to_cart_form', array($this, 'end_form_buffer_and_replace'), 999);
        }
    }

    /**
     * Start output buffering to capture add to cart form
     */
    public function start_form_buffer() {
        ob_start();
    }

    /**
     * End output buffering and replace with continue course button
     */
    public function end_form_buffer_and_replace() {
        $form_content = ob_get_clean();

        // Display continue course button instead
        $this->display_continue_course_button();
    }
    
    /**
     * Maybe disable purchase for already purchased courses
     */
    public function maybe_disable_purchase($is_purchasable, $product) {
        if (!$product) {
            return $is_purchasable;
        }
        
        // Check if this is a course product
        $course_id = $this->get_course_id_by_product($product->get_id());
        if (!$course_id) {
            return $is_purchasable;
        }
        
        // If user has purchased this course, make it non-purchasable
        if ($this->has_user_purchased_course($product->get_id())) {
            return false;
        }
        
        return $is_purchasable;
    }
    
    /**
     * Display continue course button
     */
    public function display_continue_course_button() {
        global $product;
        
        $course_id = $this->get_course_id_by_product($product->get_id());
        if (!$course_id) {
            return;
        }
        
        $course_url = get_permalink($course_id);
        
        echo '<div class="woo-custom-course-button-wrapper">';
        echo '<a href="' . esc_url($course_url) . '" class="single_add_to_cart_button button alt woo-custom-continue-course">';
        echo '<span class="course-button-icon">📚</span>';
        echo '<span class="course-button-text">' . __('Kursa Devam Et', 'woo-custom') . '</span>';
        echo '</a>';
        echo '</div>';
    }
    
    /**
     * Check if user has purchased the course product
     *
     * @param int $product_id Product ID
     * @return bool
     */
    private function has_user_purchased_course($product_id) {
        if (!is_user_logged_in()) {
            return false;
        }
        
        $user_id = get_current_user_id();
        
        // Get user's completed orders
        $orders = wc_get_orders(array(
            'customer' => $user_id,
            'status' => 'completed',
            'limit' => -1,
        ));
        
        foreach ($orders as $order) {
            $order_items = $order->get_items();
            
            foreach ($order_items as $item) {
                if ($item->get_product_id() == $product_id) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Get course ID by product ID (reuse from course info class)
     *
     * @param int $product_id Product ID
     * @return int|false Course ID or false if not found
     */
    private function get_course_id_by_product($product_id) {
        global $wpdb;

        // Method 1: Standard Tutor LMS way - find course with this product ID
        $course_id = $wpdb->get_var($wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta}
             WHERE meta_key = '_tutor_course_product_id'
             AND meta_value = %d",
            $product_id
        ));

        if ($course_id) {
            return (int) $course_id;
        }

        // Method 2: Check if product itself has course ID meta
        $direct_course_id = get_post_meta($product_id, '_course_id', true);
        if ($direct_course_id) {
            return (int) $direct_course_id;
        }

        // Method 3: Check tutor_course_id meta
        $tutor_course_id = get_post_meta($product_id, 'tutor_course_id', true);
        if ($tutor_course_id) {
            return (int) $tutor_course_id;
        }

        // Method 4: Check if product and course have same name/slug
        $product = get_post($product_id);
        if ($product) {
            $course_by_name = get_page_by_title($product->post_title, OBJECT, 'courses');
            if ($course_by_name) {
                return (int) $course_by_name->ID;
            }

            // Try by slug
            $course_by_slug = get_page_by_path($product->post_name, OBJECT, 'courses');
            if ($course_by_slug) {
                return (int) $course_by_slug->ID;
            }
        }

        // Method 5: Alternative meta key patterns
        $alternative_keys = array(
            '_tutor_course_id',
            'course_id',
            '_related_course_id'
        );

        foreach ($alternative_keys as $meta_key) {
            $course_id = get_post_meta($product_id, $meta_key, true);
            if ($course_id && is_numeric($course_id)) {
                // Verify it's actually a course
                $course = get_post($course_id);
                if ($course && $course->post_type === 'courses') {
                    return (int) $course_id;
                }
            }
        }

        return false;
    }
}
